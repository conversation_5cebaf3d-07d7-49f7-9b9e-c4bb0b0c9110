# Руководство по миграции на улучшенную систему шаблонов

## Шаги миграции

### 1. Обновление базы данных

Выполните миграцию Prisma для добавления новых полей:

```bash
npx prisma db push
```

Это добавит поля `pageTemplateId` в модели `Part` и `PartCategory`.

### 2. Обновление существующих шаблонов (опционально)

Существующие шаблоны продолжат работать без изменений. Для использования новых возможностей:

#### Пример обновления шаблона детали:

```typescript
// Старая конфигурация
{
  h1: "{{entity.name}}",
  h2: "Артикул: {{entity.sku}}",
  attributes: {
    attributeNames: ["diameter", "material", "weight"],
    withUnits: true
  }
}

// Новая конфигурация (с сохранением старой для совместимости)
{
  h1: "{{entity.name}}",
  h2: "Артикул: {{entity.sku}}",
  
  // Старое поле (сохраняется)
  attributes: {
    attributeNames: ["diameter", "material", "weight"],
    withUnits: true
  },
  
  // Новые возможности
  layout: {
    components: [
      { component: 'HeroImage', order: 1 },
      { component: 'MainTitle', order: 2 },
      { component: 'AttributeGroups', order: 3 },
      { component: 'RelatedParts', order: 4 }
    ]
  },
  
  attributeConfig: {
    groups: [
      {
        title: "Основные характеристики",
        attributes: ["diameter", "material"],
        defaultExpanded: true
      },
      {
        title: "Дополнительно",
        attributes: ["weight"],
        collapsible: true
      }
    ],
    withUnits: true
  },
  
  seo: {
    title: "{{entity.name}} - {{entity.partCategory.name}}",
    description: "{{entity.description}}"
  }
}
```

### 3. Создание специализированных шаблонов

#### Шаблон для категории "Сальники":

```typescript
const sealTemplate = {
  name: "Шаблон для сальников",
  kind: "PART",
  partConfig: {
    layout: {
      components: [
        {
          component: "HeroImage",
          props: { aspectRatio: "4:3" },
          order: 1
        },
        {
          component: "MainTitle",
          order: 2
        },
        {
          component: "AttributeTable",
          props: { 
            striped: true,
            showSearch: true 
          },
          order: 3
        },
        {
          component: "AnaloguesTable",
          props: { maxItems: 10 },
          order: 4
        },
        {
          component: "CompatibilityTable",
          conditions: [
            { type: "attribute_exists", attributeName: "equipment_model" }
          ],
          order: 5
        }
      ]
    },
    attributeConfig: {
      groups: [
        {
          title: "Размеры",
          attributes: ["inner_diameter", "outer_diameter", "width"],
          icon: "ruler"
        },
        {
          title: "Материал и свойства",
          attributes: ["material", "hardness", "temperature_range"],
          icon: "settings"
        }
      ],
      withUnits: true
    },
    seo: {
      title: "Сальник {{attr.inner_diameter.value}}×{{attr.outer_diameter.value}}×{{attr.width.value}}",
      description: "Сальник размером {{attr.inner_diameter.value}}×{{attr.outer_diameter.value}}×{{attr.width.value}} мм из материала {{attr.material.value}}"
    },
    showAnalogues: true,
    showCompatibleEquipment: true
  }
};
```

#### Шаблон для категории "Редукторы":

```typescript
const gearboxTemplate = {
  name: "Шаблон для редукторов",
  kind: "PART",
  partConfig: {
    layout: {
      components: [
        {
          component: "HeroWith3DSchema",
          props: { enable3D: true },
          conditions: [
            { type: "media_exists", mediaType: "image" }
          ],
          order: 1
        },
        {
          component: "MainTitle",
          order: 2
        },
        {
          component: "AttributeTabs",
          props: { 
            tabPosition: "top",
            defaultTab: "specifications" 
          },
          order: 3
        },
        {
          component: "TechnicalDrawing",
          conditions: [
            { type: "media_exists", mediaType: "pdf" }
          ],
          order: 4
        },
        {
          component: "SubcomponentsList",
          props: { 
            layout: "tree",
            showQuantity: true 
          },
          order: 5
        },
        {
          component: "EquipmentList",
          props: { groupByBrand: true },
          order: 6
        }
      ]
    },
    attributeConfig: {
      groups: [
        {
          title: "Технические характеристики",
          attributes: ["power", "torque", "ratio", "input_speed", "output_speed"],
          defaultExpanded: true
        },
        {
          title: "Габариты и масса",
          attributes: ["length", "width", "height", "weight"],
          collapsible: true
        },
        {
          title: "Рабочие условия",
          attributes: ["temperature_range", "efficiency", "noise_level", "protection_class"],
          collapsible: true
        }
      ],
      withUnits: true
    },
    seo: {
      title: "Редуктор {{attr.power.value}} кВт, i={{attr.ratio.value}}",
      description: "Редуктор мощностью {{attr.power.value}} кВт с передаточным числом {{attr.ratio.value}} для промышленного применения"
    },
    showSubcomponents: true,
    showTechnicalDrawings: true,
    showCompatibleEquipment: true
  }
};
```

### 4. Привязка шаблонов к категориям

```typescript
// Привязка шаблона к категории "Сальники"
await db.partCategory.update({
  where: { slug: "sealniki" },
  data: { pageTemplateId: sealTemplate.id }
});

// Привязка шаблона к категории "Редукторы"
await db.partCategory.update({
  where: { slug: "reduktory" },
  data: { pageTemplateId: gearboxTemplate.id }
});
```

### 5. Привязка шаблонов к конкретным деталям

```typescript
// Привязка специального шаблона к конкретной детали
await db.part.update({
  where: { id: 123 },
  data: { pageTemplateId: specialTemplate.id }
});
```

### 6. Тестирование

#### Проверка наследования шаблонов:

```typescript
// Тест 1: Деталь с прямой привязкой
const part1 = await db.part.findFirst({
  where: { pageTemplateId: { not: null } },
  include: { pageTemplate: true, partCategory: true }
});

const template1 = await pageTemplatesService.getTemplateByKind('PART', {
  partId: part1.id,
  partCategoryId: part1.partCategoryId
});

console.log('Источник шаблона:', template1.id === part1.pageTemplateId ? 'direct' : 'inherited');

// Тест 2: Деталь с наследованием от категории
const part2 = await db.part.findFirst({
  where: { 
    pageTemplateId: null,
    partCategory: { pageTemplateId: { not: null } }
  },
  include: { partCategory: { include: { pageTemplate: true } } }
});

const template2 = await pageTemplatesService.getTemplateByKind('PART', {
  partId: part2.id,
  partCategoryId: part2.partCategoryId
});

console.log('Источник шаблона:', template2.id === part2.partCategory.pageTemplateId ? 'category' : 'default');
```

### 7. Обновление фронтенда (опционально)

Если вы хотите использовать новые компоненты layout, обновите компоненты рендеринга:

```typescript
// Пример компонента для рендеринга layout
const LayoutRenderer: React.FC<{ layout: PageLayout; renderData: RenderData }> = ({ 
  layout, 
  renderData 
}) => {
  const sortedComponents = layout.components.sort((a, b) => a.order - b.order);
  
  return (
    <div className={layout.containerClasses}>
      {sortedComponents.map((component, index) => {
        // Проверка условий
        const shouldRender = evaluateComponentConditions(component, {
          attributes: renderData.attr,
          mediaAssets: renderData.data.mediaAssets || [],
          entity: renderData.data,
          template: renderData.template
        });
        
        if (!shouldRender.passed) return null;
        
        // Рендеринг компонента
        return (
          <ComponentRenderer
            key={index}
            component={component}
            renderData={renderData}
          />
        );
      })}
    </div>
  );
};
```

## Проверочный список миграции

- [ ] Выполнена миграция базы данных
- [ ] Созданы специализированные шаблоны для ключевых категорий
- [ ] Протестирована логика наследования шаблонов
- [ ] Проверена обратная совместимость существующих шаблонов
- [ ] Обновлены компоненты фронтенда (если требуется)
- [ ] Проведено тестирование на продакшн-данных
- [ ] Обновлена документация для команды

## Откат изменений

Если потребуется откат:

1. **Удалите новые поля** из базы данных:
```sql
ALTER TABLE "Part" DROP COLUMN "pageTemplateId";
ALTER TABLE "PartCategory" DROP COLUMN "pageTemplateId";
```

2. **Восстановите старые конфигурации** шаблонов (если они были изменены)

3. **Откатите изменения** в коде сервисов

## Поддержка

При возникновении проблем:

1. Проверьте логи сервиса PageTemplatesService
2. Убедитесь, что все миграции применены корректно
3. Проверьте валидность конфигураций шаблонов
4. Используйте утилиты валидации из `api/lib/template-utils.ts`

Новая система полностью обратно совместима, поэтому миграция может выполняться постепенно без риска для работающей системы.
