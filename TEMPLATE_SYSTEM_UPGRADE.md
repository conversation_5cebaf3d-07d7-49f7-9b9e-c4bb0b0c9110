# Улучшенная система шаблонов страниц

## Обзор изменений

Система шаблонов страниц была значительно улучшена для обеспечения максимальной персонализации страниц для разных типов запчастей. Теперь страница "Редуктора" может кардинально отличаться от страницы "Сальника" благодаря новым возможностям.

## Ключевые улучшения

### 1. Многоуровневое наследование шаблонов

**Проблема:** Раньше выбор шаблона зависел только от глобального шаблона по умолчанию.

**Решение:** Введена иерархическая система наследования:

#### Для страниц Part:
1. **Прямая привязка к детали** (`part.pageTemplateId`)
2. **Наследование от категории** (`part.partCategory.pageTemplateId`)
3. **Глобальный шаблон по умолчанию** (`isDefault: true`)

#### Для страниц Category:
1. **Прямая привязка к категории** (`category.pageTemplateId`)
2. **Глобальный шаблон по умолчанию** (`isDefault: true`)

```typescript
// Пример использования в сервисе
const template = await pageTemplatesService.getTemplateByKind('PART', {
  partId: 123,
  partCategoryId: 456,
  fallbackDefault: true
});
```

### 2. Динамическая компоновка страницы (Component-Driven UI)

**Проблема:** Жесткая структура шаблонов не позволяла создавать уникальные лэйауты.

**Решение:** Система компонентов с условной логикой:

```typescript
// Пример конфигурации layout
{
  layout: {
    components: [
      {
        component: 'HeroImage',
        props: { aspectRatio: '16:9' },
        order: 1
      },
      {
        component: 'AttributeGroups',
        props: { accordion: true },
        conditions: [
          { type: 'attribute_exists', attributeName: 'diameter' }
        ],
        order: 2
      },
      {
        component: 'HeroWith3DSchema',
        conditions: [
          { type: 'media_exists', mediaType: 'image' }
        ],
        order: 3
      }
    ]
  }
}
```

### 3. Улучшенная группировка атрибутов

**Проблема:** Простой список атрибутов неудобен для сложных деталей.

**Решение:** Группировка атрибутов по разделам:

```typescript
// Пример конфигурации групп атрибутов
{
  attributeConfig: {
    groups: [
      {
        title: "Габаритные размеры",
        attributes: ["inner_diameter", "outer_diameter", "width"],
        collapsible: true,
        defaultExpanded: true,
        icon: "ruler"
      },
      {
        title: "Материалы",
        attributes: ["material", "coating", "hardness"],
        collapsible: true,
        defaultExpanded: false
      }
    ],
    withUnits: true,
    compactMode: false
  }
}
```

### 4. SEO метаданные и производительность

Добавлена поддержка:
- SEO метаданных с интерполяцией
- Настроек производительности
- Версионирования шаблонов
- Кэширования

```typescript
// Пример SEO конфигурации
{
  seo: {
    title: "{{entity.name}} - {{entity.partCategory.name}}",
    description: "{{entity.description}} для {{attr.equipment_model.value}}",
    keywords: ["запчасти", "{{attr.brand.value}}", "{{entity.partCategory.name}}"],
    ogImage: "{{entity.image.url}}"
  }
}
```

## Доступные компоненты

### Базовые компоненты
- `HeroImage` - Главное изображение
- `HeroImageWithText` - Изображение с текстом
- `MainTitle` - Основной заголовок
- `Breadcrumbs` - Хлебные крошки
- `Description` - Описание

### Компоненты атрибутов
- `AttributeTable` - Таблица атрибутов
- `AttributeGroups` - Группы атрибутов
- `AttributeTabs` - Вкладки атрибутов
- `AttributeCards` - Карточки атрибутов
- `MainAttributes` - Основные атрибуты

### Специализированные компоненты
- `TechnicalDrawing` - Технические чертежи
- `HeroWith3DSchema` - 3D схемы
- `InteractiveSchematic` - Интерактивные схемы
- `CompatibilityTable` - Таблица совместимости
- `AnaloguesTable` - Таблица аналогов
- `SubcomponentsList` - Список подкомпонентов
- `RelatedParts` - Связанные детали
- `EquipmentList` - Список техники

### Медиа компоненты
- `ImageGallery` - Галерея изображений
- `VideoPlayer` - Видеоплеер
- `PDFViewer` - Просмотрщик PDF
- `MediaTabs` - Вкладки медиа

### Навигация и фильтры
- `CategoryFilters` - Фильтры категории
- `ProductGrid` - Сетка товаров
- `ProductList` - Список товаров
- `Pagination` - Пагинация
- `SortControls` - Контролы сортировки

## Условная логика

Компоненты могут отображаться условно:

```typescript
// Типы условий
{
  type: 'attribute_exists',
  attributeName: 'diameter'
}

{
  type: 'attribute_equals',
  attributeName: 'type',
  expectedValue: 'bearing'
}

{
  type: 'media_exists',
  mediaType: 'image'
}

{
  type: 'always' // всегда показывать
}

{
  type: 'never' // никогда не показывать
}
```

## Интерполяция переменных

Поддерживаются следующие переменные в шаблонных строках:

- `{{entity.field}}` - Поля сущности (part, category, catalogItem)
- `{{attr.name.value}}` - Значение атрибута
- `{{attr.name.unit}}` - Единица измерения атрибута
- `{{attr.name.title}}` - Название атрибута
- `{{meta.field}}` - Метаданные
- `{{var.name}}` - Пользовательские переменные

## Миграция с старой системы

### Обратная совместимость

Все существующие шаблоны продолжат работать. Старые поля сохранены:
- `attributes` → `attributeConfig`
- `filters` → `filtersConfig`
- `productAttrs` → `productAttrsConfig`

### Пошаговая миграция

1. **Создайте новый шаблон** с улучшенной конфигурацией
2. **Протестируйте** на тестовых данных
3. **Назначьте шаблон** конкретной детали или категории
4. **Постепенно мигрируйте** остальные шаблоны

## Примеры использования

### Шаблон для сальников

```typescript
{
  kind: 'PART',
  name: 'Шаблон для сальников',
  partConfig: {
    layout: {
      components: [
        { component: 'HeroImage', order: 1 },
        { component: 'MainTitle', order: 2 },
        { 
          component: 'AttributeGroups',
          props: { accordion: true },
          order: 3
        },
        { component: 'AnaloguesTable', order: 4 },
        { component: 'CompatibilityTable', order: 5 }
      ]
    },
    attributeConfig: {
      groups: [
        {
          title: "Размеры",
          attributes: ["inner_diameter", "outer_diameter", "width"]
        },
        {
          title: "Материал",
          attributes: ["material", "hardness", "temperature_range"]
        }
      ]
    },
    seo: {
      title: "Сальник {{attr.inner_diameter.value}}x{{attr.outer_diameter.value}}x{{attr.width.value}}",
      description: "Сальник размером {{attr.inner_diameter.value}}x{{attr.outer_diameter.value}}x{{attr.width.value}} мм из материала {{attr.material.value}}"
    }
  }
}
```

### Шаблон для редукторов

```typescript
{
  kind: 'PART',
  name: 'Шаблон для редукторов',
  partConfig: {
    layout: {
      components: [
        { component: 'HeroWith3DSchema', order: 1 },
        { component: 'MainTitle', order: 2 },
        { 
          component: 'AttributeTabs',
          props: { tabPosition: 'left' },
          order: 3
        },
        { component: 'SubcomponentsList', order: 4 },
        { component: 'TechnicalDrawing', order: 5 },
        { component: 'EquipmentList', order: 6 }
      ]
    },
    attributeConfig: {
      groups: [
        {
          title: "Основные характеристики",
          attributes: ["power", "torque", "ratio", "input_speed"]
        },
        {
          title: "Габариты и вес",
          attributes: ["length", "width", "height", "weight"]
        },
        {
          title: "Рабочие параметры",
          attributes: ["temperature_range", "efficiency", "noise_level"]
        }
      ]
    },
    showSubcomponents: true,
    showTechnicalDrawings: true,
    seo: {
      title: "Редуктор {{attr.power.value}} кВт, передаточное число {{attr.ratio.value}}",
      description: "Редуктор мощностью {{attr.power.value}} кВт с передаточным числом {{attr.ratio.value}} для {{attr.application.value}}"
    }
  }
}
```

## API изменения

### Новые методы в PageTemplatesService

```typescript
// Поиск шаблона с расширенными параметрами
findTemplate(params: {
  kind: 'CATEGORY' | 'PART' | 'CATALOG_ITEM';
  partId?: number;
  partCategoryId?: number;
  templateId?: string;
  fallbackDefault?: boolean;
}): Promise<PageTemplate | null>

// Получение доступных шаблонов для сущности
getAvailableTemplates(params: {
  kind: 'CATEGORY' | 'PART' | 'CATALOG_ITEM';
  partId?: number;
  partCategoryId?: number;
}): Promise<PageTemplate[]>
```

### Новые поля в моделях

```typescript
// В модели Part
interface Part {
  pageTemplateId?: string | null;
  pageTemplate?: PageTemplate | null;
  // ... остальные поля
}

// В модели PartCategory
interface PartCategory {
  pageTemplateId?: string | null;
  pageTemplate?: PageTemplate | null;
  // ... остальные поля
}
```

## Заключение

Новая система шаблонов обеспечивает:

1. **Максимальную гибкость** - каждая деталь может иметь уникальный дизайн
2. **Простоту использования** - интуитивная система компонентов
3. **Производительность** - встроенное кэширование и оптимизация
4. **SEO-оптимизацию** - автоматическая генерация метаданных
5. **Обратную совместимость** - плавная миграция без потери данных

Система готова к использованию и позволяет создавать профессиональные, персонализированные страницы для любых типов запчастей.
